export interface CSVColumn {
  id: string;
  originalName: string;
  mappedName: string;
  formulas: Formula[];
  included?: boolean;
}

export interface CSVRow {
  id: string;
  data: Record<string, string>;
  included: boolean;
}

export interface CSVData {
  columns: CSVColumn[];
  rows: CSVRow[];
  fileName: string;
}

export type FormulaType =
  | 'none'
  | 'uppercase'
  | 'lowercase'
  | 'capitalize'
  | 'trim'
  | 'replace'
  | 'concat'
  | 'split'
  | 'substring'
  | 'number'
  | 'date'
  | 'custom';

export interface Formula {
  id: string;
  type: FormulaType;
  name: string;
  description: string;
  parameters: Record<string, any>;
}

export interface FormulaDefinition {
  type: FormulaType;
  name: string;
  description: string;
  parameterDefinitions: {
    name: string;
    type: 'string' | 'number' | 'boolean' | 'column';
    description: string;
    required: boolean;
    default?: any;
  }[];
}

export interface CsvTransformation {
  id: number;
  name: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'scheduled';
  source_file: string;
  destination_file?: string;
  user_id: number;
  template_id?: number;
  records_processed?: number;
  file_size?: string;
  started_at?: string;
  completed_at?: string;
  created_at: string;
  updated_at: string;
  template?: CsvTemplate;
}

export interface CsvTemplate {
  id: number;
  name: string;
  description?: string;
  user_id: number;
  configuration: any;
  last_used_at?: string;
  usage_count: number;
  created_at: string;
  updated_at: string;
}

export interface CsvDataSource {
  id: number;
  name: string;
  type: 'database' | 'file' | 'api' | 'cloud';
  status: 'connected' | 'disconnected' | 'error';
  description?: string;
  configuration: any;
  user_id: number;
  last_sync_at?: string;
  created_at: string;
  updated_at: string;
}

// Shopify-specific types
export interface ShopifyMapping {
  [shopifyFieldKey: string]: string | null; // Maps Shopify field key to CSV column ID
}

export interface ShopifyConversionData {
  mapping: ShopifyMapping;
  isActive: boolean;
  fileName?: string;
}

export interface CSVDataWithShopify extends CSVData {
  shopifyConversion?: ShopifyConversionData;
}

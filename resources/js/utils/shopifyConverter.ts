import { CSVData, CSVColumn, ShopifyMapping } from '@/types/csv';
import { SHOPIFY_FIELDS, getShopifyFieldByKey } from './shopifyFields';

/**
 * Convert CSV data to Shopify format based on the provided mapping
 */
export const convertToShopifyFormat = (csvData: CSVData, mapping: ShopifyMapping): CSVData => {
  // Create new columns based on Shopify fields
  const shopifyColumns: CSVColumn[] = [];
  
  // Add mapped Shopify fields in the correct order
  SHOPIFY_FIELDS.forEach((shopifyField) => {
    const csvColumnId = mapping[shopifyField.key];
    if (csvColumnId) {
      // Find the original CSV column
      const originalColumn = csvData.columns.find(col => col.id === csvColumnId);
      if (originalColumn) {
        shopifyColumns.push({
          id: shopifyField.key, // Use Shopify field key as the new ID
          originalName: originalColumn.originalName,
          mappedName: shopifyField.name, // Use Shopify field name
          formulas: originalColumn.formulas || [],
          included: true
        });
      }
    }
  });

  // Transform the rows to match the new column structure
  const transformedRows = csvData.rows.map(row => {
    const newRowData: Record<string, string> = {};
    
    // Map data from original columns to Shopify columns
    shopifyColumns.forEach(shopifyColumn => {
      const shopifyFieldKey = shopifyColumn.id;
      const csvColumnId = mapping[shopifyFieldKey];
      
      if (csvColumnId && row.data[csvColumnId] !== undefined) {
        newRowData[shopifyFieldKey] = row.data[csvColumnId];
      } else {
        // Set empty value for unmapped fields
        newRowData[shopifyFieldKey] = '';
      }
    });

    return {
      ...row,
      data: newRowData
    };
  });

  // Generate a new filename with Shopify suffix
  const originalFileName = csvData.fileName;
  const nameWithoutExtension = originalFileName.replace(/\.[^/.]+$/, '');
  const shopifyFileName = `${nameWithoutExtension}_shopify_import.csv`;

  return {
    columns: shopifyColumns,
    rows: transformedRows,
    fileName: shopifyFileName
  };
};

/**
 * Validate Shopify mapping to ensure required fields are mapped
 */
export const validateShopifyMapping = (mapping: ShopifyMapping): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  // Check required fields
  const requiredFields = SHOPIFY_FIELDS.filter(field => field.required);
  requiredFields.forEach(field => {
    if (!mapping[field.key] || mapping[field.key] === '') {
      errors.push(`Required field "${field.name}" is not mapped`);
    }
  });

  // Check if at least one field is mapped
  const mappedFields = Object.values(mapping).filter(value => value !== null && value !== '');
  if (mappedFields.length === 0) {
    errors.push('At least one field must be mapped');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Get mapping statistics for display
 */
export const getMappingStats = (mapping: ShopifyMapping) => {
  const totalFields = SHOPIFY_FIELDS.length;
  const mappedFields = Object.values(mapping).filter(value => value !== null && value !== '').length;
  const requiredFields = SHOPIFY_FIELDS.filter(field => field.required).length;
  const mappedRequiredFields = SHOPIFY_FIELDS.filter(field => 
    field.required && mapping[field.key] && mapping[field.key] !== ''
  ).length;

  return {
    totalFields,
    mappedFields,
    requiredFields,
    mappedRequiredFields,
    completionPercentage: Math.round((mappedFields / totalFields) * 100),
    requiredCompletionPercentage: Math.round((mappedRequiredFields / requiredFields) * 100)
  };
};

/**
 * Generate a preview of how the data will look after conversion
 */
export const generateShopifyPreview = (csvData: CSVData, mapping: ShopifyMapping, maxRows: number = 5) => {
  const convertedData = convertToShopifyFormat(csvData, mapping);
  
  return {
    columns: convertedData.columns,
    rows: convertedData.rows.slice(0, maxRows),
    totalRows: convertedData.rows.length
  };
};

/**
 * Create a default mapping suggestion based on column name similarity
 */
export const suggestMapping = (csvData: CSVData): ShopifyMapping => {
  const mapping: ShopifyMapping = {};
  
  // Simple name matching logic
  const nameMatches: Record<string, string[]> = {
    'handle': ['handle', 'slug', 'url', 'permalink'],
    'title': ['title', 'name', 'product_name', 'product_title'],
    'body_html': ['description', 'body', 'content', 'details', 'product_description'],
    'vendor': ['vendor', 'brand', 'manufacturer', 'supplier'],
    'product_type': ['type', 'category', 'product_type', 'product_category'],
    'tags': ['tags', 'keywords', 'labels'],
    'published': ['published', 'active', 'status', 'visible'],
    'variant_sku': ['sku', 'product_code', 'item_code'],
    'variant_price': ['price', 'cost', 'amount', 'value'],
    'variant_compare_at_price': ['compare_price', 'original_price', 'msrp', 'list_price'],
    'variant_inventory_qty': ['quantity', 'stock', 'inventory', 'qty', 'available'],
    'variant_grams': ['weight', 'grams', 'mass'],
    'image_src': ['image', 'photo', 'picture', 'image_url', 'image_src'],
    'seo_title': ['seo_title', 'meta_title', 'page_title'],
    'seo_description': ['seo_description', 'meta_description', 'page_description']
  };

  // Try to match CSV columns to Shopify fields
  csvData.columns.forEach(csvColumn => {
    const columnName = csvColumn.mappedName.toLowerCase().replace(/[^a-z0-9]/g, '_');
    
    for (const [shopifyField, patterns] of Object.entries(nameMatches)) {
      if (patterns.some(pattern => columnName.includes(pattern) || pattern.includes(columnName))) {
        // Only map if not already mapped
        if (!mapping[shopifyField]) {
          mapping[shopifyField] = csvColumn.id;
        }
        break;
      }
    }
  });

  return mapping;
};

/**
 * Export the converted data as CSV string
 */
export const exportShopifyCSV = (csvData: CSVData, mapping: ShopifyMapping): string => {
  const convertedData = convertToShopifyFormat(csvData, mapping);
  
  // Create CSV header
  const headers = convertedData.columns.map(col => col.mappedName);
  let csvContent = headers.join(',') + '\n';
  
  // Add data rows
  convertedData.rows.forEach(row => {
    if (row.included) {
      const rowValues = convertedData.columns.map(col => {
        const value = row.data[col.id] || '';
        // Escape commas and quotes in CSV
        if (value.includes(',') || value.includes('"') || value.includes('\n')) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
      });
      csvContent += rowValues.join(',') + '\n';
    }
  });
  
  return csvContent;
};

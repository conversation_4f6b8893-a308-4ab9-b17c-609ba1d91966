export interface ShopifyField {
  key: string;
  name: string;
  description: string;
  required: boolean;
  type: 'text' | 'number' | 'boolean' | 'url' | 'html' | 'tags';
  category: 'product' | 'variant' | 'image' | 'seo';
  example?: string;
}

export interface ShopifyFieldCategory {
  name: string;
  description: string;
  fields: ShopifyField[];
}

export const SHOPIFY_FIELDS: ShopifyField[] = [
  // Product Fields
  {
    key: 'handle',
    name: '<PERSON><PERSON>',
    description: 'Unique identifier for the product (URL-friendly)',
    required: true,
    type: 'text',
    category: 'product',
    example: 'my-awesome-product'
  },
  {
    key: 'title',
    name: 'Title',
    description: 'Product name/title',
    required: true,
    type: 'text',
    category: 'product',
    example: 'My Awesome Product'
  },
  {
    key: 'body_html',
    name: 'Body (HTML)',
    description: 'Product description in HTML format',
    required: false,
    type: 'html',
    category: 'product',
    example: '<p>This is an amazing product...</p>'
  },
  {
    key: 'vendor',
    name: '<PERSON>endor',
    description: 'Brand or manufacturer name',
    required: false,
    type: 'text',
    category: 'product',
    example: 'ACME Corp'
  },
  {
    key: 'product_type',
    name: 'Product Type',
    description: 'Category or type of product',
    required: false,
    type: 'text',
    category: 'product',
    example: 'Electronics'
  },
  {
    key: 'tags',
    name: 'Tags',
    description: 'Comma-separated list of tags',
    required: false,
    type: 'tags',
    category: 'product',
    example: 'electronics, gadget, popular'
  },
  {
    key: 'published',
    name: 'Published',
    description: 'Whether the product is published (TRUE/FALSE)',
    required: false,
    type: 'boolean',
    category: 'product',
    example: 'TRUE'
  },
  {
    key: 'published_scope',
    name: 'Published Scope',
    description: 'Visibility scope (web, global)',
    required: false,
    type: 'text',
    category: 'product',
    example: 'web'
  },
  
  // Variant Fields
  {
    key: 'option1_name',
    name: 'Option1 Name',
    description: 'First variant option name (e.g., Size, Color)',
    required: false,
    type: 'text',
    category: 'variant',
    example: 'Size'
  },
  {
    key: 'option1_value',
    name: 'Option1 Value',
    description: 'First variant option value',
    required: false,
    type: 'text',
    category: 'variant',
    example: 'Large'
  },
  {
    key: 'option2_name',
    name: 'Option2 Name',
    description: 'Second variant option name',
    required: false,
    type: 'text',
    category: 'variant',
    example: 'Color'
  },
  {
    key: 'option2_value',
    name: 'Option2 Value',
    description: 'Second variant option value',
    required: false,
    type: 'text',
    category: 'variant',
    example: 'Red'
  },
  {
    key: 'option3_name',
    name: 'Option3 Name',
    description: 'Third variant option name',
    required: false,
    type: 'text',
    category: 'variant',
    example: 'Material'
  },
  {
    key: 'option3_value',
    name: 'Option3 Value',
    description: 'Third variant option value',
    required: false,
    type: 'text',
    category: 'variant',
    example: 'Cotton'
  },
  {
    key: 'variant_sku',
    name: 'Variant SKU',
    description: 'Stock Keeping Unit for the variant',
    required: false,
    type: 'text',
    category: 'variant',
    example: 'PROD-001-L-RED'
  },
  {
    key: 'variant_grams',
    name: 'Variant Grams',
    description: 'Weight in grams',
    required: false,
    type: 'number',
    category: 'variant',
    example: '500'
  },
  {
    key: 'variant_inventory_tracker',
    name: 'Variant Inventory Tracker',
    description: 'Inventory tracking method',
    required: false,
    type: 'text',
    category: 'variant',
    example: 'shopify'
  },
  {
    key: 'variant_inventory_qty',
    name: 'Variant Inventory Qty',
    description: 'Available quantity',
    required: false,
    type: 'number',
    category: 'variant',
    example: '100'
  },
  {
    key: 'variant_inventory_policy',
    name: 'Variant Inventory Policy',
    description: 'What to do when out of stock (deny/continue)',
    required: false,
    type: 'text',
    category: 'variant',
    example: 'deny'
  },
  {
    key: 'variant_fulfillment_service',
    name: 'Variant Fulfillment Service',
    description: 'Fulfillment service',
    required: false,
    type: 'text',
    category: 'variant',
    example: 'manual'
  },
  {
    key: 'variant_price',
    name: 'Variant Price',
    description: 'Price of the variant',
    required: false,
    type: 'number',
    category: 'variant',
    example: '29.99'
  },
  {
    key: 'variant_compare_at_price',
    name: 'Variant Compare At Price',
    description: 'Original price (for showing discounts)',
    required: false,
    type: 'number',
    category: 'variant',
    example: '39.99'
  },
  {
    key: 'variant_requires_shipping',
    name: 'Variant Requires Shipping',
    description: 'Whether shipping is required (TRUE/FALSE)',
    required: false,
    type: 'boolean',
    category: 'variant',
    example: 'TRUE'
  },
  {
    key: 'variant_taxable',
    name: 'Variant Taxable',
    description: 'Whether the variant is taxable (TRUE/FALSE)',
    required: false,
    type: 'boolean',
    category: 'variant',
    example: 'TRUE'
  },
  {
    key: 'variant_barcode',
    name: 'Variant Barcode',
    description: 'Barcode for the variant',
    required: false,
    type: 'text',
    category: 'variant',
    example: '123456789012'
  },
  
  // Image Fields
  {
    key: 'image_src',
    name: 'Image Src',
    description: 'URL of the product image',
    required: false,
    type: 'url',
    category: 'image',
    example: 'https://example.com/image.jpg'
  },
  {
    key: 'image_position',
    name: 'Image Position',
    description: 'Position of the image (1, 2, 3, etc.)',
    required: false,
    type: 'number',
    category: 'image',
    example: '1'
  },
  {
    key: 'image_alt_text',
    name: 'Image Alt Text',
    description: 'Alternative text for the image',
    required: false,
    type: 'text',
    category: 'image',
    example: 'Product image showing the front view'
  },
  
  // SEO Fields
  {
    key: 'seo_title',
    name: 'SEO Title',
    description: 'Meta title for SEO',
    required: false,
    type: 'text',
    category: 'seo',
    example: 'Buy My Awesome Product - Best Quality'
  },
  {
    key: 'seo_description',
    name: 'SEO Description',
    description: 'Meta description for SEO',
    required: false,
    type: 'text',
    category: 'seo',
    example: 'High-quality product with amazing features...'
  },
  
  // Additional Fields
  {
    key: 'gift_card',
    name: 'Gift Card',
    description: 'Whether this is a gift card (TRUE/FALSE)',
    required: false,
    type: 'boolean',
    category: 'product',
    example: 'FALSE'
  },
  {
    key: 'google_shopping_google_product_category',
    name: 'Google Shopping Category',
    description: 'Google product category for shopping ads',
    required: false,
    type: 'text',
    category: 'product',
    example: 'Electronics > Computers'
  },
  {
    key: 'google_shopping_gender',
    name: 'Google Shopping Gender',
    description: 'Target gender for Google Shopping',
    required: false,
    type: 'text',
    category: 'product',
    example: 'Unisex'
  },
  {
    key: 'google_shopping_age_group',
    name: 'Google Shopping Age Group',
    description: 'Target age group for Google Shopping',
    required: false,
    type: 'text',
    category: 'product',
    example: 'Adult'
  }
];

export const SHOPIFY_FIELD_CATEGORIES: ShopifyFieldCategory[] = [
  {
    name: 'Product Information',
    description: 'Basic product details and information',
    fields: SHOPIFY_FIELDS.filter(field => field.category === 'product')
  },
  {
    name: 'Variant Details',
    description: 'Product variant options, pricing, and inventory',
    fields: SHOPIFY_FIELDS.filter(field => field.category === 'variant')
  },
  {
    name: 'Images',
    description: 'Product images and media',
    fields: SHOPIFY_FIELDS.filter(field => field.category === 'image')
  },
  {
    name: 'SEO & Marketing',
    description: 'Search engine optimization and marketing fields',
    fields: SHOPIFY_FIELDS.filter(field => field.category === 'seo')
  }
];

export const getShopifyFieldByKey = (key: string): ShopifyField | undefined => {
  return SHOPIFY_FIELDS.find(field => field.key === key);
};

export const getRequiredShopifyFields = (): ShopifyField[] => {
  return SHOPIFY_FIELDS.filter(field => field.required);
};

export const getShopifyFieldsByCategory = (category: string): ShopifyField[] => {
  return SHOPIFY_FIELDS.filter(field => field.category === category);
};

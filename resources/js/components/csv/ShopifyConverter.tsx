import React, { useState, useEffect } from 'react';
import { CSVData, ShopifyMapping } from '@/types/csv';
import { ShopifyField, SHOPIFY_FIELD_CATEGORIES, getRequiredShopifyFields } from '@/utils/shopifyFields';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

import { CheckCircle2, AlertCircle, Info, ArrowLeft, Download } from 'lucide-react';
import { toast } from 'sonner';

interface ShopifyConverterProps {
  data: CSVData;
  onConvert: (mapping: ShopifyMapping) => void;
  onCancel: () => void;
  className?: string;
}

const ShopifyConverter: React.FC<ShopifyConverterProps> = ({
  data,
  onConvert,
  onCancel,
  className
}) => {
  const [mapping, setMapping] = useState<ShopifyMapping>({});
  const [activeTab, setActiveTab] = useState('productinformation');

  // Get available CSV columns (only included ones)
  const availableColumns = data.columns.filter(col => col.included !== false);

  // Get required fields that are not mapped
  const requiredFields = getRequiredShopifyFields();
  const unmappedRequiredFields = requiredFields.filter(field => !mapping[field.key]);

  const handleFieldMapping = (shopifyFieldKey: string, csvColumnId: string | null) => {
    setMapping(prev => ({
      ...prev,
      [shopifyFieldKey]: csvColumnId
    }));
  };

  const handleConvert = () => {
    // Check if all required fields are mapped
    if (unmappedRequiredFields.length > 0) {
      toast.error(`Please map all required fields: ${unmappedRequiredFields.map(f => f.name).join(', ')}`);
      return;
    }

    // Check if at least one field is mapped
    const mappedFields = Object.values(mapping).filter(value => value !== null && value !== '');
    if (mappedFields.length === 0) {
      toast.error('Please map at least one field to proceed');
      return;
    }

    onConvert(mapping);
    toast.success('Shopify format conversion applied successfully!');
  };

  const getMappingStatus = (field: ShopifyField) => {
    const isMapped = mapping[field.key] && mapping[field.key] !== '';
    if (field.required && !isMapped) {
      return 'required';
    }
    if (isMapped) {
      return 'mapped';
    }
    return 'optional';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'mapped':
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case 'required':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Info className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (field: ShopifyField) => {
    const status = getMappingStatus(field);
    switch (status) {
      case 'mapped':
        return <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">Mapped</Badge>;
      case 'required':
        return <Badge variant="destructive">Required</Badge>;
      default:
        return <Badge variant="secondary">Optional</Badge>;
    }
  };

  const renderFieldMapping = (field: ShopifyField) => {
    const status = getMappingStatus(field);

    return (
      <div
        key={field.key}
        className={`p-4 border rounded-lg ${
          status === 'required' ? 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/20' :
          status === 'mapped' ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950/20' :
          'border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800'
        }`}
      >
        <div className="flex items-start justify-between mb-2">
          <div className="flex items-center gap-2">
            {getStatusIcon(status)}
            <h4 className="font-medium text-sm">{field.name}</h4>
            {getStatusBadge(field)}
          </div>
        </div>

        <p className="text-xs text-gray-600 dark:text-gray-400 mb-3">
          {field.description}
        </p>

        {field.example && (
          <p className="text-xs text-gray-500 dark:text-gray-500 mb-3">
            <span className="font-medium">Example:</span> {field.example}
          </p>
        )}

        <Select
          value={mapping[field.key] || ''}
          onValueChange={(value) => handleFieldMapping(field.key, value === '' ? null : value)}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select a CSV column" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">-- No mapping --</SelectItem>
            {availableColumns.map((column) => (
              <SelectItem key={column.id} value={column.id}>
                {column.mappedName || column.originalName}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    );
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold">Convert to Shopify Format</h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Map your CSV columns to Shopify's import fields
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={onCancel} className="gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Mapping
          </Button>
          <Button
            onClick={handleConvert}
            disabled={unmappedRequiredFields.length > 0}
            className="gap-2"
          >
            <Download className="h-4 w-4" />
            Apply Conversion
          </Button>
        </div>
      </div>

      {/* Status Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Mapping Status</CardTitle>
          <CardDescription>
            Track your progress mapping CSV columns to Shopify fields
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {Object.values(mapping).filter(v => v).length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Fields Mapped</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                {unmappedRequiredFields.length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Required Fields Missing</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {availableColumns.length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Available CSV Columns</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Field Mapping Categories */}
      <div className="space-y-6">
        {/* Category Navigation */}
        <div className="flex flex-wrap gap-2 border-b border-gray-200 dark:border-gray-700">
          {SHOPIFY_FIELD_CATEGORIES.map((category) => {
            const categoryKey = category.name.toLowerCase().replace(/\s+/g, '');
            return (
              <button
                key={category.name}
                onClick={() => setActiveTab(categoryKey)}
                className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                  activeTab === categoryKey
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
                }`}
              >
                {category.name}
              </button>
            );
          })}
        </div>

        {/* Active Category Content */}
        {SHOPIFY_FIELD_CATEGORIES.map((category) => {
          const categoryKey = category.name.toLowerCase().replace(/\s+/g, '');
          if (activeTab !== categoryKey) return null;

          return (
            <div key={category.name} className="space-y-4">
              <div className="mb-4">
                <h3 className="font-medium">{category.name}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">{category.description}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {category.fields.map(renderFieldMapping)}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default ShopifyConverter;

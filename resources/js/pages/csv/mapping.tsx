import React, { useState, useEffect } from 'react';
import { Head, usePage, router } from '@inertiajs/react';
import { type SharedData } from '@/types';
import AppDashboardLayout from '@/layouts/app-dashboard-layout';
import ColumnMapper from '@/components/csv/ColumnMapper';
import TransformationStatus from '@/components/csv/TransformationStatus';
import ShopifyConverter from '@/components/csv/ShopifyConverter';
import { CSVData, CsvTransformation, ShopifyMapping } from '@/types/csv';
import { convertToShopifyFormat } from '@/utils/shopifyConverter';
import {
  FileSpreadsheet,
  Save,
  ArrowLeft,
  Download,
  Play,
  Loader2,
  RotateCw
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import axios from 'axios';
import { toast } from 'sonner';

export default function Mapping() {
  const { auth } = usePage<SharedData>().props;

  // State for CSV data
  const [csvData, setCsvData] = useState<CSVData | null>(null);
  const [filePath, setFilePath] = useState<string>('');
  const [fileName, setFileName] = useState<string>('');
  const [cacheKey, setCacheKey] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // UI state
  const [transformationName, setTransformationName] = useState('');
  const [saveAsTemplate, setSaveAsTemplate] = useState(false);
  const [templateName, setTemplateName] = useState('');
  const [selectedTemplateId, setSelectedTemplateId] = useState<string>('');
  const [templates, setTemplates] = useState<any[]>([]);
  const [isLoadingTemplates, setIsLoadingTemplates] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [transformation, setTransformation] = useState<CsvTransformation | null>(null);
  const [downloadUrl, setDownloadUrl] = useState<string | null>(null);
  const [hasChangedSinceTransformation, setHasChangedSinceTransformation] = useState(false);

  // Load data from sessionStorage on component mount
  useEffect(() => {
    try {
      console.log('Mapping page mounted, checking for stored data...');
      const storedData = sessionStorage.getItem('csvData');
      const storedFilePath = sessionStorage.getItem('filePath');
      const storedFileName = sessionStorage.getItem('fileName');
      const storedCacheKey = sessionStorage.getItem('cacheKey');

      console.log('Retrieved from sessionStorage:', {
        dataExists: !!storedData,
        filePathExists: !!storedFilePath,
        fileNameExists: !!storedFileName,
        cacheKeyExists: !!storedCacheKey
      });

      if (storedData && storedFilePath && storedFileName) {
        const parsedData = JSON.parse(storedData);
        console.log('Parsed data:', parsedData);
        setCsvData(parsedData);
        setFilePath(storedFilePath);
        setFileName(storedFileName);
        setCacheKey(storedCacheKey || '');

        // Set transformation name based on file name
        const baseName = storedFileName ? storedFileName.split('.')[0] : 'Transformation';
        setTransformationName(`${baseName} - ${new Date().toLocaleDateString()}`);

        // Clear the session storage to avoid stale data on refresh
        sessionStorage.removeItem('csvData');
        sessionStorage.removeItem('filePath');
        sessionStorage.removeItem('fileName');
        sessionStorage.removeItem('cacheKey');

        // Set loading to false
        setIsLoading(false);
      } else {
        // Redirect to upload page if no data is available
        setIsLoading(false);
        toast.error('No CSV data available. Redirecting to upload page...');
        setTimeout(() => {
          router.visit(route('csv.upload.page'));
        }, 1500);
        return;
      }
    } catch (error) {
      console.error('Error loading CSV data:', error);
      setIsLoading(false);
      toast.error('Error loading CSV data. Redirecting to upload page...');
      setTimeout(() => {
        router.visit(route('csv.upload.page'));
      }, 1500);
      return;
    }

    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      setIsLoadingTemplates(true);
      console.log('Fetching templates...');
      const response = await axios.get(route('csv.templates.list'));

      console.log('Templates response:', response.data);

      if (response.data.success) {
        setTemplates(response.data.templates);
        console.log('Templates set:', response.data.templates);
      } else {
        console.error('Failed to fetch templates:', response.data.message);
        toast.error(response.data.message || 'Failed to load templates');
      }
    } catch (error) {
      console.error('Error fetching templates:', error);
      toast.error('Failed to load templates');
    } finally {
      setIsLoadingTemplates(false);
    }
  };

  const handleApplyTemplate = async () => {
    if (!selectedTemplateId || !csvData) return;

    try {
      setIsLoading(true);

      console.log('Applying template with ID:', selectedTemplateId);
      console.log('Current CSV data:', csvData);

      const response = await axios.post(route('csv.templates.apply'), {
        template_id: selectedTemplateId,
        data: JSON.stringify(csvData),
      });

      console.log('Template apply response:', response.data);

      if (response.data.success) {
        // Check if the response data has formulas
        const columnsWithFormulas = response.data.data.columns.filter(col => col.formulas && col.formulas.length > 0);
        console.log('Columns with formulas after applying template:', columnsWithFormulas);

        setCsvData(response.data.data);
        toast.success('Template applied successfully');

        // If there's already a transformation, mark that changes have been made
        if (transformation) {
          setHasChangedSinceTransformation(true);
        }
      } else {
        toast.error(response.data.message || 'Failed to apply template');
      }
    } catch (error) {
      console.error('Error applying template:', error);
      toast.error('Failed to apply template');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveTemplate = async () => {
    if (!csvData) return;

    if (!templateName.trim()) {
      toast.error('Please enter a template name');
      return;
    }

    try {
      setIsLoading(true);

      // Check if any columns have formulas
      const columnsWithFormulas = csvData.columns.filter(col => col.formulas && col.formulas.length > 0);

      if (columnsWithFormulas.length === 0) {
        // Ask for confirmation if no formulas are defined
        if (!confirm('This template does not have any formulas defined. Are you sure you want to save it?')) {
          setIsLoading(false);
          return;
        }
      }

      // Log the data being saved
      console.log('Saving template with columns:', csvData.columns);
      console.log('Columns with formulas:', columnsWithFormulas);

      // Make sure we're sending the full column data including formulas
      const columnsToSave = csvData.columns.map(col => ({
        ...col,
        formulas: col.formulas || []
      }));

      console.log('Saving template with columns:', columnsToSave);
      console.log('Columns with formulas:', columnsToSave.filter(col => col.formulas.length > 0));

      console.log('Saving template with name:', templateName);
      console.log('Using route:', route('csv.templates.store'));

      const response = await axios.post(route('csv.templates.store'), {
        name: templateName,
        description: 'Template created on ' + new Date().toLocaleString(),
        configuration: JSON.stringify({
          columns: columnsToSave,
        }),
      });

      console.log('Template save response:', response.data);

      // Verify the template was saved by fetching it immediately
      const verifyResponse = await axios.get(route('csv.templates.list'));
      console.log('Verify templates response:', verifyResponse.data);

      if (response.data.success) {
        toast.success('Template saved successfully');
        setSaveAsTemplate(false);
        setTemplateName('');

        // Wait a moment before fetching templates to ensure the database has updated
        setTimeout(() => {
          fetchTemplates();
        }, 1000);
      } else {
        toast.error(response.data.message || 'Failed to save template');
      }
    } catch (error) {
      console.error('Error saving template:', error);
      toast.error('Failed to save template');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStartTransformation = async () => {
    if (!csvData || !filePath) return;

    try {
      setIsLoading(true);

      // Log the data being sent
      console.log('CSV Data being sent:', csvData);
      console.log('Columns with formulas:', csvData.columns.filter(col => col.formulas && col.formulas.length > 0));

      const response = await axios.post(route('csv.transformations.store'), {
        name: transformationName,
        source_file: filePath,
        data: JSON.stringify(csvData),
        template_id: saveAsTemplate && templateName ? null : selectedTemplateId || null,
        save_template: saveAsTemplate,
        template_name: templateName,
      });

      if (response.data.success) {
        setTransformation(response.data.transformation);
        setHasChangedSinceTransformation(false); // Reset the change flag
        toast.success('Transformation started');
      } else {
        toast.error(response.data.message || 'Failed to start transformation');
      }
    } catch (error) {
      console.error('Error starting transformation:', error);
      toast.error('Failed to start transformation');
    } finally {
      setIsLoading(false);
    }
  };

  const handleExport = async () => {
    if (!csvData) return;

    try {
      setIsExporting(true);
      setIsLoading(true);

      const response = await axios.post(route('csv.export'), {
        data: JSON.stringify(csvData),
        file_name: fileName,
      });

      if (response.data.success) {
        setDownloadUrl(response.data.download_url);
        toast.success('CSV exported successfully');
      } else {
        toast.error(response.data.message || 'Failed to export CSV');
      }
    } catch (error) {
      console.error('Error exporting CSV:', error);
      toast.error('Failed to export CSV');
    } finally {
      setIsExporting(false);
      setIsLoading(false);
    }
  };

  const handleDownload = () => {
    if (downloadUrl) {
      window.location.href = downloadUrl;
    }
  };

  // Use a ref to track if we've already shown the completion toast
  const hasShownCompletionToast = React.useRef(false);

  const handleTransformationComplete = (url: string) => {
    setDownloadUrl(url);

    // Only show the toast once
    if (!hasShownCompletionToast.current) {
      toast.success('Transformation completed successfully! You can now download the result.');
      hasShownCompletionToast.current = true;
    }
  };

  const handleRetryTransformation = async () => {
    if (!transformation) return;

    try {
      setIsLoading(true);

      const response = await axios.post(route('csv.transformations.retry', { id: transformation.id }));

      if (response.data.success) {
        setTransformation(response.data.transformation);
        toast.success('Transformation restarted');
      } else {
        toast.error(response.data.message || 'Failed to restart transformation');
      }
    } catch (error) {
      console.error('Error restarting transformation:', error);
      toast.error('Failed to restart transformation');
    } finally {
      setIsLoading(false);
    }
  };

  // Only show the no data message if we're not loading and there's no data
  if (!isLoading && (!csvData || !filePath || !fileName)) {
    return (
      <AppDashboardLayout user={auth.user}>
        <Head title="CSV Mapping" />
        <div className="flex flex-col items-center justify-center h-64">
          <p className="text-muted-foreground mb-4">No CSV data available</p>
          <Button onClick={() => router.visit(route('csv.upload.page'))}>
            Upload a CSV File
          </Button>
        </div>
      </AppDashboardLayout>
    );
  }

  return (
      <AppDashboardLayout user={auth.user}>
          <Head title="CSV Mapping" />

          {isLoading ? (
              <div className="flex h-[60vh] flex-col items-center justify-center">
                  <Loader2 className="text-primary mb-4 h-12 w-12 animate-spin" />
                  <h2 className="mb-2 text-xl font-medium">Loading CSV Data</h2>
                  <p className="text-muted-foreground">Please wait while we prepare your data for mapping...</p>
              </div>
          ) : (
              <>
                  <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                      <div>
                          <h1 className="text-2xl font-bold">CSV Mapping</h1>
                          <p className="mt-1 text-gray-500 dark:text-gray-400">Map columns and apply transformations to your CSV data</p>
                      </div>

                      <div className="flex flex-wrap gap-2">
                          <Button variant="outline" onClick={() => router.visit(route('csv.upload.page'))} disabled={isLoading} className="gap-2">
                              <ArrowLeft className="h-4 w-4" />
                              Back to Upload
                          </Button>

                          {!transformation && (
                              <Button onClick={handleStartTransformation} disabled={isLoading} className="gap-2">
                                  {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Play className="h-4 w-4" />}
                                  Start Transformation
                              </Button>
                          )}

                          {transformation && hasChangedSinceTransformation && (
                              <Button onClick={handleStartTransformation} disabled={isLoading} className="gap-2">
                                  {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <RotateCw className="h-4 w-4" />}
                                  Re-transform
                              </Button>
                          )}

                          {!transformation && (
                              <Button variant="outline" onClick={handleExport} disabled={isExporting || !csvData} className="gap-2">
                                  {isExporting ? <Loader2 className="h-4 w-4 animate-spin" /> : <Download className="h-4 w-4" />}
                                  Export CSV
                              </Button>
                          )}

                          {downloadUrl && (
                              <Button variant="outline" onClick={handleDownload} className="gap-2">
                                  <Download className="h-4 w-4" />
                                  Download Result
                              </Button>
                          )}
                      </div>
                  </div>
                  <div className="grid grid-cols-1 gap-6 lg:grid-cols-4">
                      <div className="lg:col-span-3">
                          {/* File Info */}
                          <div className="mb-6 rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
                              <div className="flex items-center gap-3">
                                  <FileSpreadsheet className="text-primary h-6 w-6" />
                                  <div>
                                      <h3 className="font-medium">{fileName}</h3>
                                      <p className="text-muted-foreground text-sm">
                                          {csvData?.rows?.length || 0} rows, {csvData?.columns?.length || 0} columns
                                      </p>
                                  </div>
                              </div>
                          </div>

                          {/* Column Mapper */}
                          <ColumnMapper
                              data={csvData}
                              onDataChange={(newData) => {
                                  setCsvData(newData);

                                  // If there's already a transformation, mark that changes have been made
                                  if (transformation) {
                                      setHasChangedSinceTransformation(true);
                                  }
                              }}
                          />
                      </div>

                      <div>
                          {/* Transformation Options */}
                          <div className="mb-6 rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
                              <h3 className="mb-4 font-medium">Transformation Options</h3>

                              <div className="space-y-4">
                                  <div>
                                      <label className="mb-1 block text-sm font-medium">Transformation Name</label>
                                      <Input
                                          value={transformationName}
                                          onChange={(e) => setTransformationName(e.target.value)}
                                          placeholder="Enter a name for this transformation"
                                      />
                                  </div>

                                  <div className="flex items-center gap-2">
                                      <Checkbox
                                          id="save-template"
                                          checked={saveAsTemplate}
                                          onCheckedChange={(checked) => {
                                              setSaveAsTemplate(checked === true);
                                              if (checked === true) {
                                                  setSelectedTemplateId('');
                                              }
                                          }}
                                      />
                                      <label htmlFor="save-template" className="cursor-pointer text-sm">
                                          Save as template
                                      </label>
                                  </div>

                                  {saveAsTemplate && (
                                      <div>
                                          <label className="mb-1 block text-sm font-medium">Template Name</label>
                                          <div className="flex gap-2">
                                              <Input
                                                  value={templateName}
                                                  onChange={(e) => setTemplateName(e.target.value)}
                                                  placeholder="Enter template name"
                                              />
                                              <Button
                                                  variant="outline"
                                                  onClick={handleSaveTemplate}
                                                  disabled={isLoading || !templateName.trim()}
                                                  className="gap-2 whitespace-nowrap"
                                              >
                                                  <Save className="h-4 w-4" />
                                                  Save Template
                                              </Button>
                                          </div>
                                          <p className="text-muted-foreground mt-2 text-xs">
                                              Save the current column mappings and formulas as a reusable template.
                                          </p>
                                      </div>
                                  )}

                                  {!saveAsTemplate && (
                                      <div>
                                          <label className="mb-1 block text-sm font-medium">Apply Template</label>
                                          <div className="flex gap-2">
                                              <Select value={selectedTemplateId} onValueChange={setSelectedTemplateId}>
                                                  <SelectTrigger className="w-full">
                                                      <SelectValue placeholder="Select a template" />
                                                  </SelectTrigger>
                                                  <SelectContent>
                                                      {isLoadingTemplates ? (
                                                          <div className="text-muted-foreground p-2 text-center text-sm">Loading templates...</div>
                                                      ) : templates.length > 0 ? (
                                                          templates.map((template) => (
                                                              <SelectItem key={template.id} value={template.id.toString()}>
                                                                  {template.name}
                                                              </SelectItem>
                                                          ))
                                                      ) : (
                                                          <div className="text-muted-foreground p-2 text-center text-sm">
                                                              No templates available. Save a template first.
                                                          </div>
                                                      )}
                                                  </SelectContent>
                                              </Select>
                                              <Button
                                                  variant="outline"
                                                  onClick={handleApplyTemplate}
                                                  disabled={isLoading || !selectedTemplateId || templates.length === 0}
                                                  className="gap-2 whitespace-nowrap"
                                              >
                                                  <RotateCw className="h-4 w-4" />
                                                  Apply Template
                                              </Button>
                                          </div>
                                          <p className="text-muted-foreground mt-2 text-xs">
                                              Apply a saved template to quickly set up column mappings and formulas.
                                          </p>
                                      </div>
                                  )}
                              </div>
                          </div>

                          {/* Transformation Status */}
                          {transformation && (
                              <TransformationStatus
                                  transformation={transformation}
                                  onComplete={handleTransformationComplete}
                                  onRetry={handleRetryTransformation}
                              />
                          )}
                      </div>
                  </div>
              </>
          )}
      </AppDashboardLayout>
  );
}
